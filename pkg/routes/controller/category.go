package controller

import (
	"net/http"
	"store-backend/pkg/models"
	"store-backend/pkg/models/dto"
	"store-backend/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetCategories(action *gin.Context) {
	var fetch, categories []models.Category
	ctx := action.Request.Context()
	cursor, err := models.GetCategoryCollection().Find(ctx, bson.M{})

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error fetching categories"})
		return
	}

	defer cursor.Close(ctx)

	if err := cursor.All(ctx, &fetch); err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error parsing categories"})
		return
	}

	for _, category := range fetch {
		categories = append(categories, models.Category{ID: category.ID, Name: category.Name})
	}

	action.JSON(http.StatusOK, categories)
}

func GetCategory(action *gin.Context) {
	var category models.Category
	byParam := action.Param("id")
	ctx := action.Request.Context()
	categoryId, _ := primitive.ObjectIDFromHex(byParam)

	err := models.GetCategoryCollection().FindOne(ctx, bson.M{"_id": categoryId}).Decode(&category)

	if err != nil {
		action.JSON(http.StatusNotFound, gin.H{"error": utils.NotFoundError})
		return
	}

	action.JSON(http.StatusOK, category)
}

func CreateCategory(action *gin.Context) {
	var payload dto.CategoryPayload
	ctx := action.Request.Context()

	if err := action.BindJSON(&payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": utils.InvalidPayloadError, "reasons": utils.ErrorFormat(err)})
		return
	}

	if err := validation.Struct(payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	newRecord := models.Category{Name: payload.Name}
	inserted, err := models.GetCategoryCollection().InsertOne(ctx, newRecord)

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating category"})
		return
	}

	action.JSON(http.StatusCreated, gin.H{"message": "Category created", "data": inserted})
}

func UpdateCategory(action *gin.Context) {
	var payload dto.CategoryPayload
	byParam := action.Param("id")
	ctx := action.Request.Context()
	categoryId, _ := primitive.ObjectIDFromHex(byParam)

	if err := action.BindJSON(&payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": utils.InvalidPayloadError, "reasons": utils.ErrorFormat(err)})
		return
	}

	if err := validation.Struct(payload); err != nil {
		action.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updated, err := models.GetCategoryCollection().UpdateOne(ctx, bson.M{"_id": categoryId}, bson.M{"$set": payload})

	if err != nil {
		action.JSON(http.StatusInternalServerError, gin.H{"error": "Error updating category"})
		return
	}

	if updated.MatchedCount == 0 {
		action.JSON(http.StatusNotFound, gin.H{"error": utils.NotFoundError})
		return
	}

	action.JSON(http.StatusOK, gin.H{"message": "Category updated"})
}
